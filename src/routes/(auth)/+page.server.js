import { redirect } from '@sveltejs/kit';
import { getEnvironmentConfig } from '$lib/utils/environment.js';

/** @type {import('./$types').PageServerLoad} */
export async function load({ locals, url }) {
    // Log all URL parameters for debugging
    console.log('🔍 Auth route params:', {
        all: Object.fromEntries(url.searchParams.entries()),
        type: url.searchParams.get('type'),
        ticket: url.searchParams.get('ticket'),
        refreshToken: url.searchParams.get('refreshToken'),
        token: url.searchParams.get('token'),
        redirectTo: url.searchParams.get('redirectTo'),
        email: url.searchParams.get('email')
    });
    
    // Check for nHost email redirect parameters
    const type = url.searchParams.get('type');
    const ticket = url.searchParams.get('ticket');
    const refreshToken = url.searchParams.get('refreshToken');
    const token = url.searchParams.get('token');
    
    // Handle email verification redirect
    // nHost sends: ticket=...&type=emailConfirmation&redirectTo=...
    if (type === 'emailConfirmation' && ticket) {
        console.log('📧 Redirecting to email verification with ticket');
        throw redirect(307, `/verify?ticket=${ticket}&type=${type}`);
    }
    
    // Legacy support for old refreshToken approach
    if (type === 'emailConfirmation' && refreshToken) {
        console.log('📧 Redirecting to email verification with refreshToken (legacy)');
        throw redirect(307, `/verify?refreshToken=${refreshToken}&type=${type}`);
    }
    
    // Handle password reset redirect  
    // nHost sends: ticket=...&type=passwordReset&redirectTo=...
    if (type === 'passwordReset' && ticket) {
        console.log('🔑 Redirecting to password reset with ticket');
        throw redirect(307, `/resetpassword?ticket=${ticket}&type=${type}`);
    }
    
    // IMPORTANT: For password reset, we should NOT use refreshToken/token from the redirect
    // because these are often expired by the time user reaches the form.
    // Instead, we need to extract the original ticket from the email link
    if (type === 'passwordReset' && (refreshToken || token)) {
        console.log('⚠️ Password reset with expired token - need to handle differently');
        // Don't redirect with the expired token, instead show an error or resend flow
        throw redirect(307, `/forgotpassword?message=reset_link_expired&email=${url.searchParams.get('email') || ''}`);
    }
    
    // If user is authenticated, redirect to dashboard
    if (locals.user) {
        throw redirect(307, '/dashboard');
    }
    
    const envConfig = getEnvironmentConfig();
    
    // Return data for unauthenticated users
    return {
        authenticated: false,
        turnstileSiteKey: envConfig.turnstile.siteKey
    };
};
