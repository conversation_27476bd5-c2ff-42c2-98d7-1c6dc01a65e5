<script lang="ts">
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { onMount } from 'svelte';
  import { authActions } from '$lib/stores/auth.js';
  import { nhost } from '$lib/stores/nhost.js';
  import toast from 'svelte-5-french-toast';
  
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import { Eye, EyeOff, ArrowLeft } from 'lucide-svelte';

  interface Props {
    resetToken: string;
    needsAutoSignin?: boolean;
  }

  let { resetToken, needsAutoSignin = false }: Props = $props();

  let password = $state('');
  let confirmPassword = $state('');
  let showPassword = $state(false);
  let showConfirmPassword = $state(false);
  let isLoading = $state(false);
  let isSigningIn = $state(false);
  let isSignedIn = $state(false);

  onMount(async () => {
    // If we have a processed token from nHost, try to sign in immediately
    if (needsAutoSignin && resetToken) {
      await handleAutoSignin();
    }
  });

  async function handleAutoSignin() {
    isSigningIn = true;
    console.log('🔄 Auto-signing in with token for password reset...', {
      tokenLength: resetToken?.length,
      tokenPrefix: resetToken?.substring(0, 20) + '...',
      needsAutoSignin
    });
    
    try {
      // Use the token as a refreshToken to sign in
      const result = await nhost.auth.refreshSession(resetToken);
      
      if (result.error) {
        console.error('❌ Auto-signin failed:', result.error);
        
        // Check if token is expired or invalid
        if (result.error.message?.includes('expired') || 
            result.error.message?.includes('invalid') ||
            result.error.message?.includes('not found')) {
          toast.error('Reset link has expired. Please request a new one.');
          setTimeout(() => {
            goto('/forgotpassword?message=reset_link_expired');
          }, 2000);
        } else {
          toast.error('Failed to authenticate reset link. Please try again.');
          setTimeout(() => {
            goto('/forgotpassword?message=auth_failed');
          }, 2000);
        }
        return;
      }
      
      console.log('✅ Auto-signin successful');
      isSignedIn = true;
      toast.success('Ready to reset your password!');
      
    } catch (error) {
      console.error('🚨 Auto-signin error:', error);
      toast.error('Reset link has expired. Please request a new one.');
      setTimeout(() => {
        goto('/forgotpassword?message=reset_link_expired');
      }, 2000);
    } finally {
      isSigningIn = false;
    }
  }

  async function handleSubmit(event: Event) {
    event.preventDefault();
    
    if (!password || !confirmPassword) {
      toast.error('Please fill in all fields');
      return;
    }

    if (password !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    if (password.length < 8) {
      toast.error('Password must be at least 8 characters long');
      return;
    }

    isLoading = true;

    try {
      let result;
      
      if (isSignedIn) {
        // User is signed in, use simple changePassword
        console.log('🔑 Changing password for signed-in user...');
        result = await nhost.auth.changePassword({ newPassword: password });
        
        // Sign out after password change for security
        if (!result.error) {
          await nhost.auth.signOut();
        }
      } else {
        // Try ticket-based approach (for original tickets)
        console.log('🎫 Using ticket-based password reset...');
        result = await authActions.resetPassword(password, resetToken);
      }
      
      if (result.error) {
        // Handle specific error codes
        if (result.error.code === 'TOKEN_EXPIRED') {
          toast.error('Password reset link has expired. Redirecting to request a new one...');
          setTimeout(() => {
            goto('/forgotpassword?message=reset_link_expired');
          }, 2000);
          return;
        }
        
        toast.error(result.error.message || 'Failed to reset password');
        return;
      }

      toast.success('Password reset successfully! You can now sign in with your new password.');
      goto('/');
      
    } catch (error) {
      console.error('Reset password error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      isLoading = false;
    }
  }
</script>

<svelte:head>
  <title>Reset Password - SourceFlex</title>
</svelte:head>

<div class="reset-password-form">
  <div class="form-container">
    <!-- Back Navigation -->
    <div class="back-navigation">
      <a href="/forgotpassword" class="back-link">
        <ArrowLeft class="h-4 w-4" />
        Back to forgot password
      </a>
    </div>
    
    <div class="form-header">
      <h2 class="form-title">Set new password</h2>
      <p class="form-subtitle">
        Enter your new password below
      </p>
    </div>

    <form class="auth-form" onsubmit={handleSubmit}>
      <!-- Password Field -->
      <div class="form-group">
        <Label for="password">New Password</Label>
        <div class="password-wrapper">
          <Input
            id="password"
            type={showPassword ? 'text' : 'password'}
            bind:value={password}
            placeholder="Enter new password"
            disabled={isLoading}
            required
            class="pr-10"
          />
          <button
            type="button"
            class="password-toggle"
            onclick={() => showPassword = !showPassword}
          >
            {#if showPassword}
              <EyeOff class="h-4 w-4" />
            {:else}
              <Eye class="h-4 w-4" />
            {/if}
          </button>
        </div>
        <p class="field-hint">
          Must be at least 8 characters long
        </p>
      </div>

      <!-- Confirm Password Field -->
      <div class="form-group">
        <Label for="confirm-password">Confirm New Password</Label>
        <div class="password-wrapper">
          <Input
            id="confirm-password"
            type={showConfirmPassword ? 'text' : 'password'}
            bind:value={confirmPassword}
            placeholder="Confirm new password"
            disabled={isLoading}
            required
            class="pr-10"
          />
          <button
            type="button"
            class="password-toggle"
            onclick={() => showConfirmPassword = !showConfirmPassword}
          >
            {#if showConfirmPassword}
              <EyeOff class="h-4 w-4" />
            {:else}
              <Eye class="h-4 w-4" />
            {/if}
          </button>
        </div>
      </div>

      <Button type="submit" class="w-full submit-button" disabled={isLoading || !resetToken}>
        {#if isLoading}
          <div class="loading-spinner"></div>
          Updating password...
        {:else}
          Update password
        {/if}
      </Button>

      <div class="back-link">
        <a href="/" class="back-link-text">
          Back to login
        </a>
      </div>
    </form>
  </div>
</div>

<style>
  .reset-password-form {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    position: relative;
    z-index: 10;
  }

  .form-container {
    width: 100%;
    max-width: 28rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(12px);
    border-radius: 16px;
    padding: 2.5rem;
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: formSlideIn 0.8s ease-out;
  }

  @keyframes formSlideIn {
    from {
      opacity: 0;
      transform: translateY(30px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .form-header {
    text-align: center;
    margin-bottom: 2rem;
    animation: headerFadeIn 1s ease-out 0.2s both;
  }

  @keyframes headerFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .form-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: hsl(var(--foreground));
    margin-bottom: 0.5rem;
    letter-spacing: -0.025em;
  }

  .form-subtitle {
    font-size: 0.875rem;
    color: hsl(var(--muted-foreground));
    line-height: 1.5;
  }

  .auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    animation: fieldSlideIn 0.6s ease-out;
  }

  @keyframes fieldSlideIn {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .password-wrapper {
    position: relative;
    transition: all 0.3s ease;
  }

  .password-wrapper:focus-within {
    transform: translateY(-1px);
  }

  .password-toggle {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: hsl(var(--muted-foreground));
    cursor: pointer;
    padding: 0.375rem;
    border-radius: 6px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .password-toggle:hover {
    color: hsl(var(--foreground));
    background: linear-gradient(135deg, hsl(var(--muted)), rgba(0, 0, 0, 0.05));
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .password-toggle:active {
    transform: translateY(-50%) scale(0.95);
  }

  .field-hint {
    font-size: 0.75rem;
    color: hsl(var(--muted-foreground));
    margin-top: -0.25rem;
  }



  :global(.submit-button) {
    margin-top: 0.5rem;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  :global(.submit-button:hover:not(:disabled)) {
    transform: translateY(-1px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  :global(.submit-button:active:not(:disabled)) {
    transform: translateY(0);
  }

  .loading-spinner {
    width: 18px;
    height: 18px;
    border: 2.5px solid transparent;
    border-top: 2.5px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.75rem;
    display: inline-block;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  .back-navigation {
    margin-bottom: 1rem;
  }

  .back-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: hsl(var(--primary));
    text-decoration: none;
    font-weight: 500;
  }

  .back-link:hover {
    text-decoration: underline;
  }

  /* Mobile Responsiveness */
  @media (max-width: 640px) {
    .reset-password-form {
      padding: 1.5rem 1rem;
    }

    .form-container {
      padding: 2rem 1.5rem;
    }

    .form-title {
      font-size: 1.5rem;
    }
  }
</style>
