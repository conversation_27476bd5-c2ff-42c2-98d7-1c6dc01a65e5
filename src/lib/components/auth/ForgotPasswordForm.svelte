<script lang="ts">
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { onMount } from 'svelte';
  import { authActions } from '$lib/stores/auth.js';
  import toast from 'svelte-5-french-toast';
  import { Card, CardContent } from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import { CheckCircle, ArrowLeft } from 'lucide-svelte';

  interface Props {
    turnstileToken: string;
  }

  let { turnstileToken }: Props = $props();

  let email = $state('');
  let isLoading = $state(false);
  let isEmailSent = $state(false);

  onMount(() => {
    const urlEmail = $page.url.searchParams.get('email');
    if (urlEmail) {
      email = urlEmail;
    }
    
    // Check for messages from redirects
    const message = $page.url.searchParams.get('message');
    if (message === 'expired') {
      toast.error('Your password reset link has expired. Please request a new one.');
    } else if (message === 'reset_link_expired') {
      toast.error('The reset link has expired. Please request a new password reset.');
    }
  });

  async function handleSubmit(event: Event) {
    event.preventDefault();
    
    if (!email) {
      toast.error('Please enter your email address');
      return;
    }

    isLoading = true;

    try {
      const result = await authActions.sendPasswordResetEmail(email);
      
      if (result.error) {
        console.error('Password reset error:', result.error);
        
        // Handle specific error cases
        if (result.error.message?.includes('unverified') || 
            result.error.message?.includes('not verified')) {
          toast.error('Please verify your email address first before resetting your password.');
          
          // Offer to resend verification
          setTimeout(() => {
            if (confirm('Would you like us to resend the email verification link?')) {
              goto(`/resendverification?email=${encodeURIComponent(email)}`);
            }
          }, 2000);
          return;
        }
        
        if (result.error.message?.includes('not found') || 
            result.error.message?.includes('user not found')) {
          toast.error('No account found with this email address. Please check the email or create a new account.');
          return;
        }
        
        toast.error(result.error.message || 'Failed to send reset email');
        return;
      }

      isEmailSent = true;
      toast.success('Password reset email sent! Please check your inbox.');
      
    } catch (error) {
      console.error('Password reset error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      isLoading = false;
    }
  }
</script>

<svelte:head>
  <title>Reset Password - SourceFlex</title>
</svelte:head>

<div class="auth-container">
  <Card class="auth-card">
    <CardContent class="auth-content">
      {#if isEmailSent}
        <!-- Success State -->
        <div class="success-state">
          <div class="success-icon">
            <CheckCircle size={48} />
          </div>
          
          <div class="success-content">
            <h3 class="success-title">Email sent successfully!</h3>
            <p class="success-message">
              We've sent a password reset link to <strong>{email}</strong>. 
              Please check your inbox and follow the instructions to reset your password.
            </p>
            
            <div class="success-actions">
              <a href="/" class="back-link">
                <ArrowLeft size={16} />
                Back to login
              </a>
            </div>
          </div>
        </div>
      {:else}
        <!-- Form State -->
        <form onsubmit={handleSubmit} class="auth-form">
          <div class="form-group">
            <Label for="email">Email address</Label>
            <Input
              id="email"
              type="email"
              bind:value={email}
              placeholder="Enter your email address"
              disabled={isLoading}
              required
            />
          </div>

          <Button type="submit" disabled={isLoading} class="w-full">
            {#if isLoading}
              <div class="loading-spinner"></div>
              Sending reset link...
            {:else}
              Send reset link
            {/if}
          </Button>

          <div class="back-to-login">
            <a href="/" class="back-link">
              <ArrowLeft size={16} />
              Back to login
            </a>
          </div>
        </form>
      {/if}
    </CardContent>
  </Card>
</div>

<style>
  .auth-container {
    width: 100%;
    max-width: 420px;
    margin: 0 auto;
  }

  :global(.auth-card) {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(226, 232, 240, 0.8);
    box-shadow: 
      0 10px 25px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05),
      0 0 0 1px rgba(0, 0, 0, 0.05);
    border-radius: 20px;
    overflow: hidden;
    backdrop-filter: blur(10px);
  }

  :global(.dark .auth-card) {
    background: rgba(2, 6, 23, 0.95);
    border: 1px solid rgba(30, 41, 59, 0.8);
    box-shadow: 
      0 10px 25px -3px rgba(0, 0, 0, 0.4),
      0 4px 6px -2px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(148, 163, 184, 0.1);
  }

  :global(.auth-content) {
    padding-top: 1.5rem;
  }

  .success-state {
    text-align: center;
    padding: 1rem 0;
  }

  .success-icon {
    display: flex;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: rgb(34, 197, 94);
  }

  .success-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .success-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: hsl(var(--foreground));
    margin-bottom: 0.75rem;
  }

  .success-message {
    color: hsl(var(--muted-foreground));
    line-height: 1.5;
    margin-bottom: 1.5rem;
  }

  .success-actions {
    margin-top: 2rem;
  }

  .auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    animation: formFadeIn 0.8s ease-out 0.2s both;
  }

  @keyframes formFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .form-group {
    animation: fieldSlideIn 0.6s ease-out;
  }

  @keyframes fieldSlideIn {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }


  .loading-spinner {
    width: 18px;
    height: 18px;
    border: 2.5px solid transparent;
    border-top: 2.5px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  .back-to-login {
    text-align: center;
    margin-top: 0.5rem;
  }

  .back-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: hsl(var(--primary));
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
  }

  .back-link:hover {
    background: hsl(var(--muted));
  }

  /* Mobile Optimizations */
  @media (max-width: 640px) {
    .auth-container {
      max-width: 100%;
      padding: 0 1rem;
    }

    :global(.auth-card) {
      border-radius: 16px;
    }

    :global(.auth-content) {
      padding: 1rem;
    }

    .success-message {
      font-size: 0.875rem;
    }
  }
</style>