<script lang="ts">
  import ProtectedRoute from '$lib/components/ProtectedRoute.svelte';
  import AppLayout from '$lib/components/layouts/AppLayout.svelte';
  import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
  import { goto } from '$app/navigation';
  import { session } from '$lib/stores/auth.js';

  function handleContinue() {
    goto('/dashboard');
  }
</script>

<ProtectedRoute>
  <AppLayout>
    <div class="container mx-auto max-w-2xl py-12">
      <Card>
        <CardHeader class="text-center">
          <CardTitle class="text-2xl font-bold">Welcome to SourceFlex!</CardTitle>
          <p class="text-muted-foreground">
            Your email has been verified successfully. Let's get you started.
          </p>
        </CardHeader>
        <CardContent class="space-y-6">
          <div class="text-center">
            <h3 class="text-lg font-semibold mb-2">Hello, {$session?.user?.displayName || $session?.user?.email}!</h3>
            <p class="text-muted-foreground">
              We're excited to have you on board. Your journey with SourceFlex starts here.
            </p>
          </div>
          
          <div class="flex justify-center">
            <Button onclick={handleContinue} class="w-full max-w-sm">
              Continue to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</ProtectedRoute>
