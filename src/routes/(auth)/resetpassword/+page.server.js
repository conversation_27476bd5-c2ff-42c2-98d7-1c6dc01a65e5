import { redirect } from '@sveltejs/kit';
import { getEnvironmentConfig } from '$lib/utils/environment.js';

/** @type {import('./$types').PageServerLoad} */
export async function load({ locals, url }) {
    // If user is authenticated, redirect to dashboard
    if (locals.user) {
        throw redirect(307, '/dashboard');
    }
    
    // Check for reset parameters in URL params
    const ticket = url.searchParams.get('ticket');  // Original ticket (if available)
    const token = url.searchParams.get('token');     // Processed token from nHost
    const type = url.searchParams.get('type');
    
    console.log('🔑 Reset password page params:', {
        ticket,
        token,
        type,
        all: Object.fromEntries(url.searchParams.entries())
    });
    
    // Use ticket first (if available), then fall back to token
    const resetToken = ticket || token;
    
    if (!resetToken) {
        // No token provided, redirect to forgot password page
        console.log('❌ No reset token found, redirecting to forgot password');
        throw redirect(307, '/forgotpassword');
    }
    
    const envConfig = getEnvironmentConfig();
    
    // Return data for password reset
    return {
        authenticated: false,
        resetToken: resetToken,
        resetType: type,
        isTicket: !!ticket, // True if we have original ticket
        isToken: !!token,   // True if we have processed token
        needsAutoSignin: !!token && !ticket, // True if we need to signin first
        turnstileSiteKey: envConfig.turnstile.siteKey
    };
};
