import { redirect } from '@sveltejs/kit';
import { getEnvironmentConfig } from '$lib/utils/environment.js';

/** @type {import('./$types').PageServerLoad} */
export async function load({ locals, url }) {
    // If user is authenticated, redirect to dashboard
    if (locals.user) {
        throw redirect(307, '/dashboard');
    }
    
    // Check for verification parameters from nHost
    const ticket = url.searchParams.get('ticket');        // nHost standard
    const refreshToken = url.searchParams.get('refreshToken'); // Legacy support
    const type = url.searchParams.get('type');
    const email = url.searchParams.get('email'); // Optional, may not be present
    
    console.log('📧 Verify page params:', {
        ticket,
        refreshToken,
        type,
        email,
        all: Object.fromEntries(url.searchParams.entries())
    });
    
    // Use ticket first (nHost standard), then fall back to refreshToken (legacy)
    const verificationToken = ticket || refreshToken;
    
    const envConfig = getEnvironmentConfig();
    
    // Return data for email verification
    return {
        authenticated: false,
        ticket: ticket,                     // nHost standard
        refreshToken: refreshToken,         // Legacy support
        verificationToken: verificationToken, // Universal token
        type: type,
        email: email,
        isTicket: !!ticket, // Flag to indicate if it's a ticket vs legacy refreshToken
        turnstileSiteKey: envConfig.turnstile.siteKey
    };
};
